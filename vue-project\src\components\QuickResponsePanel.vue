<script setup>
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  startQuickResponse as socketStartQuickResponse,
  getSocket
} from '../services/socketService';

const props = defineProps({
  teacherId: {
    type: String,
    default: 'teacher1'
  },
  autoRefresh: {
    type: Boolean,
    default: true
  }
});

// 当前抢答
const currentResponse = ref(null);
// 正在创建抢答
const isCreating = ref(false);
// 抢答表单
const responseForm = reactive({
  title: '',
  description: '',
  timeLimit: 30 // 默认30秒
});
// 倒计时
const countdown = ref(0);
// 倒计时定时器
let countdownTimer = null;
// 刷新定时器
let refreshTimer = null;

// 创建抢答
const createQuickResponse = async () => {
  if (!responseForm.title.trim()) {
    ElMessage.warning('请输入抢答标题');
    return;
  }

  try {
    const responseData = {
      title: responseForm.title,
      description: responseForm.description,
      timeLimit: responseForm.timeLimit
    };

    // 通过Socket发起抢答
    socketStartQuickResponse(responseData);

    // 设置当前抢答数据（临时）
    currentResponse.value = {
      ...responseData,
      id: `temp_${Date.now()}`,
      status: 'active',
      responses: [],
      startTime: new Date().toISOString()
    };

    isCreating.value = false;

    // 重置表单
    responseForm.title = '';
    responseForm.description = '';
    responseForm.timeLimit = 30;

    // 开始倒计时
    startCountdown(responseForm.timeLimit);

    ElMessage.success('抢答已发起');
  } catch (error) {
    console.error('发起抢答失败:', error);
    ElMessage.error('发起抢答失败');
  }
};

// 取消创建
const cancelCreate = () => {
  isCreating.value = false;
  
  // 重置表单
  responseForm.title = '';
  responseForm.description = '';
  responseForm.timeLimit = 30;
};

// 结束抢答
const endCurrentResponse = async () => {
  if (!currentResponse.value) return;

  try {
    // 通过Socket结束抢答
    const socket = getSocket();
    if (socket) {
      socket.emit('end-quick-response', currentResponse.value.id);
    }

    // 停止倒计时
    stopCountdown();

    // 更新状态
    currentResponse.value.status = 'ended';

    ElMessage.success('抢答已结束');
  } catch (error) {
    console.error('结束抢答失败:', error);
    ElMessage.error('结束抢答失败');
  }
};

// 确认结束抢答
const confirmEndResponse = () => {
  ElMessageBox.confirm(
    '确定要结束当前抢答吗？',
    '结束抢答',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    endCurrentResponse();
  }).catch(() => {});
};

// 加载当前抢答（现在主要用于初始化）
const loadCurrentResponse = async (responseId) => {
  // 由于现在使用Socket实时通信，这个方法主要用于初始化
  // 实际的抢答数据会通过Socket事件更新
  console.log('初始化抢答面板');
};

// 清除当前抢答
const clearCurrentResponse = () => {
  currentResponse.value = null;
  stopCountdown();
};

// 开始倒计时
const startCountdown = (seconds) => {
  // 先停止已有的倒计时
  stopCountdown();
  
  countdown.value = seconds;
  
  countdownTimer = setInterval(() => {
    countdown.value--;
    
    if (countdown.value <= 0) {
      stopCountdown();
      // 时间到，自动结束抢答
      endCurrentResponse();
    }
  }, 1000);
};

// 停止倒计时
const stopCountdown = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
    countdownTimer = null;
  }
};

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '';
  
  const date = new Date(timestamp);
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit',
    second: '2-digit'
  });
};

// 格式化倒计时
const formatCountdown = (seconds) => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// 获取学生信息
const getStudentInfo = (studentId) => {
  // 模拟数据，实际应从用户系统获取
  const students = {
    'student1': { name: '张三', avatar: 'https://xsgames.co/randomusers/avatar.php?g=pixel&key=1' },
    'student2': { name: '李四', avatar: 'https://xsgames.co/randomusers/avatar.php?g=pixel&key=2' },
    'student3': { name: '王五', avatar: 'https://xsgames.co/randomusers/avatar.php?g=pixel&key=3' }
  };
  
  return students[studentId] || { name: '未知学生', avatar: '' };
};

onMounted(() => {
  // 初始加载
  loadCurrentResponse();

  // 监听Socket事件
  const socket = getSocket();
  if (socket) {
    // 监听抢答创建确认
    socket.on('quick-response-created', (data) => {
      console.log('抢答创建确认:', data);
      if (data.success && currentResponse.value) {
        currentResponse.value.id = data.responseId;
      }
    });

    // 监听抢答提交
    socket.on('quick-response-submitted', (answerData) => {
      console.log('收到抢答提交:', answerData);
      if (currentResponse.value && answerData.responseId === currentResponse.value.id) {
        // 添加到抢答列表
        currentResponse.value.responses.push({
          studentId: answerData.userId,
          studentName: answerData.userName,
          answer: answerData.answer,
          timestamp: answerData.timestamp
        });
      }
    });

    // 监听学生请求当前抢答
    socket.on('request-current-quick-response', (data) => {
      console.log('收到学生请求当前抢答:', data);

      // 如果有活动的抢答，发送给请求的学生
      if (currentResponse.value && currentResponse.value.status === 'active') {
        console.log('向学生发送当前活动抢答:', currentResponse.value.title);
        socket.to(data.requestSocketId).emit('active-quick-response-response', {
          quickResponse: currentResponse.value
        });
      } else {
        console.log('当前没有活动抢答');
        socket.to(data.requestSocketId).emit('active-quick-response-response', {
          quickResponse: null
        });
      }
    });

    // 监听抢答结束确认
    socket.on('quick-response-end-confirmed', (data) => {
      console.log('抢答结束确认:', data);
      if (data.success && currentResponse.value) {
        currentResponse.value.status = 'ended';
        stopCountdown();
      }
    });
  }
});

onBeforeUnmount(() => {
  stopCountdown();

  if (refreshTimer) {
    clearInterval(refreshTimer);
  }

  // 移除Socket事件监听
  const socket = getSocket();
  if (socket) {
    socket.off('quick-response-created');
    socket.off('quick-response-submitted');
    socket.off('quick-response-end-confirmed');
    socket.off('request-current-quick-response');
  }
});
</script>

<template>
  <div class="quick-response-panel">
    <!-- 抢答创建表单 -->
    <div v-if="isCreating" class="response-create-form">
      <h3 class="form-title">发起抢答</h3>
      
      <el-form :model="responseForm" label-width="80px">
        <el-form-item label="标题">
          <el-input v-model="responseForm.title" placeholder="请输入抢答标题" />
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input 
            v-model="responseForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入抢答描述（可选）" 
          />
        </el-form-item>
        
        <el-form-item label="时间限制">
          <el-input-number 
            v-model="responseForm.timeLimit" 
            :min="5" 
            :max="300"
            :step="5"
          />
          <span class="time-unit">秒</span>
        </el-form-item>
        
        <el-form-item>
          <div class="form-actions">
            <el-button @click="cancelCreate">取消</el-button>
            <el-button type="primary" @click="createQuickResponse">发起</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 当前抢答展示 -->
    <div v-else-if="currentResponse" class="current-response">
      <div class="response-header">
        <div class="response-title">
          <h3>{{ currentResponse.title }}</h3>
          <div class="response-status">
            <el-tag 
              :type="currentResponse.status === 'active' ? 'success' : 'info'"
              size="small"
            >
              {{ currentResponse.status === 'active' ? '进行中' : '已结束' }}
            </el-tag>
            
            <!-- 计时器 -->
            <div v-if="currentResponse.status === 'active' && countdown > 0" class="countdown">
              <el-icon><Timer /></el-icon>
              <span>{{ formatCountdown(countdown) }}</span>
            </div>
          </div>
        </div>
        
        <div v-if="currentResponse.description" class="response-description">
          {{ currentResponse.description }}
        </div>
      </div>
      
      <div class="response-content">
        <div class="response-stats">
          <div class="stat-item">
            <div class="stat-value">{{ currentResponse.responses.length }}</div>
            <div class="stat-label">已抢答</div>
          </div>
        </div>
        
        <div class="response-list">
          <div v-if="currentResponse.responses.length === 0" class="empty-tip">
            <el-icon><Loading /></el-icon>
            <span>等待学生抢答...</span>
          </div>
          
          <div 
            v-for="(response, index) in currentResponse.responses"
            :key="response.studentId"
            class="response-item"
          >
            <div class="response-rank">{{ index + 1 }}</div>
            
            <el-avatar 
              :src="getStudentInfo(response.studentId).avatar" 
              :size="40"
            />
            
            <div class="response-info">
              <div class="student-name">
                {{ getStudentInfo(response.studentId).name }}
              </div>
              <div class="response-time">
                {{ formatTime(response.timestamp) }}
              </div>
            </div>
            
            <div v-if="response.answer" class="response-answer">
              {{ response.answer }}
            </div>
          </div>
        </div>
      </div>
      
      <div class="response-actions">
        <el-button v-if="currentResponse.status === 'active'" type="warning" @click="confirmEndResponse">
          结束抢答
        </el-button>
        <el-button v-else type="info" @click="clearCurrentResponse">
          关闭抢答
        </el-button>
      </div>
    </div>
    
    <!-- 没有抢答时的空状态 -->
    <div v-else class="empty-state">
      <el-empty description="暂无抢答">
        <el-button type="primary" @click="isCreating = true">发起抢答</el-button>
      </el-empty>
    </div>
  </div>
</template>

<style scoped>
.quick-response-panel {
  height: 100%;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: white;
  display: flex;
  flex-direction: column;
}

.response-create-form {
  padding: 20px;
}

.form-title {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 500;
}

.time-unit {
  margin-left: 5px;
  color: #606266;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
}

.current-response {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.response-header {
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
}

.response-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.response-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.response-status {
  display: flex;
  align-items: center;
}

.countdown {
  margin-left: 10px;
  display: flex;
  align-items: center;
  color: #f56c6c;
  font-weight: 500;
}

.countdown .el-icon {
  margin-right: 5px;
}

.response-description {
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
}

.response-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.response-stats {
  padding: 15px;
  display: flex;
  border-bottom: 1px solid #ebeef5;
}

.stat-item {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 10px 15px;
  text-align: center;
}

.stat-value {
  font-size: 20px;
  font-weight: 500;
  color: #409eff;
}

.stat-label {
  font-size: 12px;
  color: #606266;
}

.response-list {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.empty-tip {
  text-align: center;
  color: #909399;
  padding: 30px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-tip .el-icon {
  font-size: 24px;
  margin-bottom: 10px;
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.response-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 10px;
  border: 1px solid #ebeef5;
}

.response-rank {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #f5f7fa;
  color: #606266;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  margin-right: 10px;
}

.response-item:nth-child(1) .response-rank {
  background-color: #f56c6c;
  color: white;
}

.response-item:nth-child(2) .response-rank {
  background-color: #e6a23c;
  color: white;
}

.response-item:nth-child(3) .response-rank {
  background-color: #409eff;
  color: white;
}

.response-info {
  margin-left: 10px;
  flex: 1;
}

.student-name {
  font-weight: 500;
  margin-bottom: 5px;
}

.response-time {
  font-size: 12px;
  color: #909399;
}

.response-answer {
  background-color: #f5f7fa;
  padding: 5px 10px;
  border-radius: 4px;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.response-actions {
  padding: 15px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #ebeef5;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}
</style> 