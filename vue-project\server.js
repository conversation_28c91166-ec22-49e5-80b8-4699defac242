/**
 * WebSocket服务器 - 用于教师端和学生端的通信
 * 实现教学过程中的实时交互
 */
import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import cors from 'cors';

const app = express();
app.use(cors());

// 添加ping端点
app.get('/', (req, res) => {
  res.status(200).send('WebSocket服务器运行中');
});

const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: '*',  // 开发环境允许所有来源
    methods: ['GET', 'POST']
  }
});

// 用户信息存储
const connectedUsers = new Map();
let teacherSocket = null;
let controlGrantedTo = null;

// 课堂信息存储
const classrooms = new Map(); // 存储课堂信息，key为课堂码

// 考试信息存储
const activeExams = new Map(); // 存储活跃的考试，key为考试ID

// 学生提问存储（持久化）
const classroomQuestions = new Map(); // 存储每个课堂的提问，key为课堂码

// 生成随机课堂码
function generateClassCode() {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let code = '';
  let attempts = 0;
  const maxAttempts = 10; // 最大尝试次数
  
  // 生成唯一的课堂码
  do {
    code = '';
  for (let i = 0; i < 6; i++) {
    code += characters.charAt(Math.floor(Math.random() * characters.length));
  }
    attempts++;
    
    // 如果尝试次数过多，加入时间戳确保唯一性
    if (attempts >= maxAttempts) {
      const timestamp = Date.now().toString(36).substring(0, 2).toUpperCase();
      code = code.substring(0, 4) + timestamp;
      break;
    }
  } while (classrooms.has(code)); // 确保课堂码不重复
  
  console.log(`生成课堂码成功: ${code} (尝试次数: ${attempts})`);
  return code;
}

// 当有新连接时
io.on('connection', (socket) => {
  console.log('新连接建立:', socket.id);
  
  // 用户登录
  socket.on('user-login', (userData) => {
    const { userId, userName, role } = userData;
    
    console.log(`用户登录: ${userName} (${role}, ID: ${userId})`);
    
    // 将用户信息与socket关联
    connectedUsers.set(socket.id, {
      userId,
      userName,
      role,
      socketId: socket.id,
      status: 'online',
      joinedAt: new Date().toISOString()
    });
    
    // 如果是教师，记录教师socket
    if (role === '教师') {
      teacherSocket = socket;
      console.log(`教师已连接: ${userName}`);
    }
    
    // 广播更新后的用户列表
    io.emit('users-updated', Array.from(connectedUsers.values()));
    
    // 如果用户已经在某个课堂中（重连的情况），发送课堂信息更新
    const userClassroom = Array.from(classrooms.values()).find(classroom => 
      classroom.students.some(s => s.userId === userId) || classroom.teacherId === userId
    );
    
    if (userClassroom) {
      console.log(`用户 ${userName} 重连到课堂 ${userClassroom.id}`);
      
      // 更新学生记录中的socketId
      if (userClassroom.students.some(s => s.userId === userId)) {
        const student = userClassroom.students.find(s => s.userId === userId);
        student.socketId = socket.id;
        student.userName = userName; // 确保名字是最新的
        
        // 将socket加入课堂房间
        socket.join(userClassroom.id);
        
        // 向学生发送加入成功通知
        socket.emit('join-classroom-success', {
          classCode: userClassroom.id,
          teacherName: userClassroom.teacherName,
          message: `成功重连到 ${userClassroom.teacherName} 的课堂`
        });
        
        // 通知所有人更新学生列表
        io.to(userClassroom.id).emit('classroom-students-updated', {
          students: userClassroom.students
        });
      }
    }
  });
  
  // 课堂管理相关事件
  // 创建新课堂
  socket.on('create-classroom', (data, callback) => {
    try {
    const { userId, userName } = data;
      if (!userId || !userName) {
        console.error('创建课堂失败：缺少用户信息', data);
        if (callback) callback({ error: '缺少用户信息' });
        return;
      }
      
      console.log(`收到create-classroom事件，用户ID: ${userId}, 用户名: ${userName}`);
      
      // 检查该用户是否已经创建了其他课堂
      const existingClassroom = Array.from(classrooms.values()).find(c => c.teacherId === userId);
      if (existingClassroom) {
        console.log(`用户 ${userName} 已经创建了课堂 ${existingClassroom.id}，将使用现有课堂`);
        
        // 更新教师的socketId
        existingClassroom.teacherSocketId = socket.id;
        
        // 将socket加入课堂房间
        socket.join(existingClassroom.id);
        
        // 返回现有课堂码
        socket.emit('classroom-created', {
          classCode: existingClassroom.id,
          message: `成功重连到课堂，课堂码: ${existingClassroom.id}`
        });
        
        if (callback) callback({ success: true, classCode: existingClassroom.id });
        return;
      }
      
      // 生成新的课堂码
    const classCode = generateClassCode();
      console.log(`生成课堂码: ${classCode}`);
    
    // 创建课堂
    classrooms.set(classCode, {
      id: classCode,
      teacherId: userId,
      teacherName: userName,
      teacherSocketId: socket.id,
      students: [],
      createdAt: new Date().toISOString()
    });
    
    // 将socket加入课堂房间
    socket.join(classCode);
    
    console.log(`教师 ${userName} 创建了课堂，课堂码: ${classCode}`);
    
    // 返回课堂码给教师
    socket.emit('classroom-created', {
      classCode,
      message: `成功创建课堂，课堂码: ${classCode}`
    });
      
      // 打印当前所有课堂
      console.log(`当前所有课堂: ${Array.from(classrooms.keys()).join(', ')}`);
      
      // 如果提供了回调函数，返回成功
      if (callback) callback({ success: true, classCode });
    } catch (error) {
      console.error('创建课堂时发生错误:', error);
      if (callback) callback({ error: '服务器内部错误' });
    }
  });
  
  // 学生加入课堂
  socket.on('join-classroom', (data) => {
    const { userId, userName, classCode } = data;
    const user = connectedUsers.get(socket.id);
    
    if (!user) {
      socket.emit('join-classroom-error', {
        message: '用户未登录，请先登录'
      });
      return;
    }
    
    // 优先使用传入的用户名，确保展示学生的注册名字
    const studentName = userName || user.userName;
    
    // 更新connectedUsers中的用户名，确保一致性
    if (user.userName !== studentName && studentName) {
      user.userName = studentName;
      // 更新用户列表
      io.emit('users-updated', Array.from(connectedUsers.values()));
    }
    
    console.log(`用户尝试加入课堂: ${studentName} (ID: ${userId}), 课堂码: ${classCode}`);
    
    // 检查课堂是否存在
    if (!classrooms.has(classCode)) {
      socket.emit('join-classroom-error', {
        message: '课堂码无效，请检查后重试'
      });
      return;
    }
    
    // 获取课堂信息
    const classroom = classrooms.get(classCode);
    
    // 检查是否已在课堂中
    const existingStudent = classroom.students.find(s => s.userId === userId);
    if (existingStudent) {
      // 更新socket ID和用户名，确保名字一致性
      existingStudent.socketId = socket.id;
      existingStudent.userName = studentName; // 使用学生的注册名字
      existingStudent.role = user.role;
    } else {
      // 添加到课堂学生列表
      classroom.students.push({
        userId,
        userName: studentName, // 使用学生的注册名字
        socketId: socket.id,
        role: user.role,
        joinedAt: new Date().toISOString()
      });
    }
    
    // 将socket加入课堂房间
    socket.join(classCode);
    
    console.log(`学生 ${studentName} 加入了课堂，课堂码: ${classCode}`);
    
    // 通知学生成功加入
    socket.emit('join-classroom-success', {
      classCode,
      teacherName: classroom.teacherName,
      message: `成功加入 ${classroom.teacherName} 的课堂`
    });
    
    // 通知教师有新学生加入
    if (classroom.teacherSocketId) {
      io.to(classroom.teacherSocketId).emit('student-joined', {
        userId,
        userName: studentName,
        classCode
      });
    }
    
    // 更新在线学生列表
    io.to(classCode).emit('classroom-students-updated', {
      students: classroom.students
    });
  });
  
  // 退出课堂
  socket.on('leave-classroom', (data) => {
    const { userId, classCode } = data;
    
    if (classrooms.has(classCode)) {
      const classroom = classrooms.get(classCode);
      
      // 从学生列表中移除
      classroom.students = classroom.students.filter(s => s.userId !== userId);
      
      // 离开房间
      socket.leave(classCode);
      
      console.log(`用户 ${userId} 退出了课堂，课堂码: ${classCode}`);
      
      // 更新在线学生列表
      io.to(classCode).emit('classroom-students-updated', {
        students: classroom.students
      });
    }
  });
  
  // 投票相关
  socket.on('create-vote', (voteData) => {
    // 确保投票数据有唯一ID
    voteData.id = voteData.id || `vote_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    console.log('新投票创建:', voteData.title, 'ID:', voteData.id);
    
    // 广播给所有用户
    io.emit('new-vote', voteData);
    
    // 确认创建成功
    socket.emit('vote-created', {
      success: true,
      voteId: voteData.id,
      message: '投票创建成功'
    });
  });
  
  socket.on('submit-vote', (voteResult) => {
    const user = connectedUsers.get(socket.id);
    if (!user) {
      socket.emit('vote-error', {
        message: '用户未登录，无法提交投票'
      });
      return;
    }
    
    console.log('收到投票:', voteResult, '来自用户:', user.userName);
    
    // 确保投票结果包含用户信息
    const enhancedVoteResult = {
      ...voteResult,
      userName: user.userName,
      userRole: user.role,
      timestamp: new Date().toISOString()
    };
    
    // 广播投票更新给所有用户
    io.emit('vote-updated', enhancedVoteResult);
    
    // 确认投票提交成功
    socket.emit('vote-submitted', {
      success: true,
      voteId: voteResult.voteId,
      message: '投票提交成功'
    });
  });
  
  socket.on('end-vote', (voteId) => {
    console.log('投票结束:', voteId);
    
    // 广播投票结束消息
    io.emit('vote-ended', {
      voteId,
      endedAt: new Date().toISOString()
    });
    
    // 确认投票结束成功
    socket.emit('vote-end-confirmed', {
      success: true,
      voteId,
      message: '投票已结束'
    });
  });
  
  // 控制权相关
  socket.on('grant-control', (targetUserId) => {
    if (!teacherSocket || socket.id !== teacherSocket.id) {
      return; // 只允许教师授予控制权
    }
    
    // 查找targetUserId对应的用户
    const targetUser = Array.from(connectedUsers.values()).find(u => u.userId === targetUserId);
    if (!targetUser) {
      console.log(`授权目标用户不存在: ${targetUserId}`);
      socket.emit('control-error', {
        message: '授权目标用户不存在或已离线'
      });
      return;
    }
    
    console.log(`控制权授予: ${targetUserId} (${targetUser.userName}), socketId: ${targetUser.socketId}`);
    // 存储socketId而不仅是userId，确保正确的socket拥有控制权
    controlGrantedTo = targetUser.socketId;
    
    // 向所有客户端广播控制权变更，确保包含完整的学生信息
    io.emit('control-granted', {
      userId: targetUserId,
      userName: targetUser.userName,
      socketId: targetUser.socketId,
      hasFullAccess: true, // 给予学生完整的功能访问权限
      timestamp: new Date().toISOString()
    });
    
    // 特别通知被授权的学生
    io.to(targetUser.socketId).emit('control-granted-to-you', {
      message: '您已获得控制权，现在可以控制PPT和使用所有教师端功能',
      hasFullAccess: true // 给予学生完整的功能访问权限
    });
  });
  
  socket.on('revoke-control', () => {
    if (!teacherSocket || socket.id !== teacherSocket.id) {
      return; // 只允许教师收回控制权
    }
    
    console.log('控制权收回');
    controlGrantedTo = null;
    io.emit('control-revoked');
  });
  
  // 页面控制命令
  socket.on('slide-change', (slideData) => {
    const user = connectedUsers.get(socket.id);
    if (!user) return;
    
    // 检查是否有控制权：教师或被授权的用户
    const controllingUser = Array.from(connectedUsers.values()).find(u => u.socketId === controlGrantedTo);
    const hasControl = socket.id === teacherSocket?.id || (controllingUser && user.userId === controllingUser.userId);

    if (hasControl) {
      console.log(`页面切换: ${slideData.slideIndex}, 由 ${user.userName} (${user.role}) 控制`);
      
      // 广播给其他所有用户，包括完整的用户信息
      socket.broadcast.emit('slide-changed', {
        ...slideData,
        controllerName: user.userName,
        controllerRole: user.role === '学生' && socket.id === controlGrantedTo ? '授权学生' : user.role,
        controllerId: user.userId,
        timestamp: new Date().toISOString()
      });
    } else {
      // 已移除权限警告输出，避免控制台信息过多
      // 静默拒绝页面控制操作，不发送任何提示
    }
  });
  
  // 白板绘图事件
  socket.on('whiteboard-draw', (drawData) => {
    const user = connectedUsers.get(socket.id);
    if (!user) return;
    
    // 检查是否有控制权：教师或被授权的用户
    const controllingUser = Array.from(connectedUsers.values()).find(u => u.socketId === controlGrantedTo);
    const hasControl = socket.id === teacherSocket?.id || (controllingUser && user.userId === controllingUser.userId);
    
    if (hasControl) {
      console.log(`接收到绘图操作: ${drawData.type} 来自 ${drawData.userName} (${user.role})`);
      
      // 添加控制者信息
      const enhancedDrawData = {
        ...drawData,
        controllerName: user.userName,
        controllerRole: user.role
      };
      
      // 广播绘图操作给所有客户端
      socket.broadcast.emit('whiteboard-draw', enhancedDrawData);
    } else {
      // 已移除权限警告输出，避免控制台信息过多
      // 静默拒绝绘图操作，不发送任何提示
    }
  });
  
  // 学生提问
  socket.on('ask-question', (questionData) => {
    console.log('收到提问:', questionData);

    // 查找当前课堂
    const currentRoom = Array.from(classrooms.values()).find(room =>
      room.students.some(student => student.socketId === socket.id)
    );

    if (currentRoom) {
      // 为问题生成唯一ID
      const questionId = `q_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      // 完善问题数据
      const completeQuestionData = {
        ...questionData,
        id: questionId,
        classCode: currentRoom.id,
        receivedAt: new Date().toISOString(),
        answered: false,
        answer: null,
        answeredAt: null,
        answeredBy: null
      };

      // 存储到课堂问题列表
      if (!classroomQuestions.has(currentRoom.id)) {
        classroomQuestions.set(currentRoom.id, []);
      }
      classroomQuestions.get(currentRoom.id).unshift(completeQuestionData);

      // 发送给教师
      if (currentRoom.teacherSocketId) {
        io.to(currentRoom.teacherSocketId).emit('new-question', completeQuestionData);
        console.log('提问已转发给教师:', currentRoom.teacherName);
      }

      // 确认提问已收到
      socket.emit('question-submitted', {
        success: true,
        questionId: questionId,
        message: '问题已提交'
      });
    } else {
      console.log('未找到对应的课堂');
      socket.emit('question-error', {
        message: '未找到对应的课堂，请确保已加入课堂'
      });
    }
  });

  // 教师回答问题
  socket.on('answer-question', (answerData) => {
    console.log('收到教师回答:', answerData);

    // 查找当前课堂
    const currentRoom = Array.from(classrooms.values()).find(room =>
      room.teacherSocketId === socket.id
    );

    if (currentRoom && classroomQuestions.has(currentRoom.id)) {
      // 更新问题状态
      const questions = classroomQuestions.get(currentRoom.id);
      const questionIndex = questions.findIndex(q => q.id === answerData.questionId);

      if (questionIndex !== -1) {
        questions[questionIndex].answered = true;
        questions[questionIndex].answer = answerData.answer;
        questions[questionIndex].answeredAt = answerData.answeredAt;
        questions[questionIndex].answeredBy = answerData.answeredBy;

        // 找到提问的学生并发送回答
        const student = currentRoom.students.find(s => s.userId === answerData.studentId);
        if (student) {
          io.to(student.socketId).emit('question-answered', {
            ...answerData,
            questionData: questions[questionIndex]
          });
          console.log('回答已发送给学生:', student.userName);
        }

        // 通知教师回答已保存
        socket.emit('answer-saved', {
          success: true,
          questionId: answerData.questionId,
          message: '回答已保存并发送给学生'
        });
      }
    }
  });

  // 获取课堂问题历史
  socket.on('get-classroom-questions', (data) => {
    const { classCode } = data;
    const user = connectedUsers.get(socket.id);

    if (!user) return;

    // 检查用户是否有权限查看问题
    const classroom = classrooms.get(classCode);
    if (!classroom) return;

    const isTeacher = classroom.teacherSocketId === socket.id;
    const isStudent = classroom.students.some(s => s.socketId === socket.id);

    if (isTeacher || isStudent) {
      const questions = classroomQuestions.get(classCode) || [];

      // 如果是学生，只返回自己的问题
      const filteredQuestions = isStudent ?
        questions.filter(q => q.userId === user.userId) :
        questions;

      socket.emit('classroom-questions-history', {
        classCode,
        questions: filteredQuestions
      });
    }
  });

  // 抢答相关事件
  socket.on('start-quick-response', (responseData) => {
    const user = connectedUsers.get(socket.id);
    if (!user) {
      socket.emit('quick-response-error', {
        message: '用户未登录，无法发起抢答'
      });
      return;
    }

    // 确保抢答数据有唯一ID
    responseData.id = responseData.id || `qr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    responseData.teacherId = user.userId;
    responseData.teacherName = user.userName;
    responseData.startTime = new Date().toISOString();
    responseData.status = 'active';
    responseData.responses = [];

    console.log('新抢答发起:', responseData.title, 'ID:', responseData.id, '教师:', user.userName);

    // 找到教师所在的课堂
    const teacherClassroom = Array.from(classrooms.values()).find(c => c.teacherId === user.userId);

    if (teacherClassroom) {
      console.log(`向课堂 ${teacherClassroom.id} 广播抢答事件，学生数量: ${teacherClassroom.students.length}`);

      // 向课堂内的所有用户广播（包括教师和学生）
      io.to(teacherClassroom.id).emit('quick-response-started', responseData);

      // 同时向教师本人发送（确保教师也能收到）
      socket.emit('quick-response-started', responseData);
    } else {
      console.log('未找到教师所在的课堂，向所有用户广播');
      // 如果找不到课堂，则向所有用户广播（兼容性处理）
      io.emit('quick-response-started', responseData);
    }

    // 确认创建成功
    socket.emit('quick-response-created', {
      success: true,
      responseId: responseData.id,
      message: '抢答发起成功'
    });
  });

  socket.on('submit-quick-response', (answerData) => {
    const user = connectedUsers.get(socket.id);
    if (!user) {
      socket.emit('quick-response-error', {
        message: '用户未登录，无法提交抢答'
      });
      return;
    }

    console.log('收到抢答提交:', answerData, '来自用户:', user.userName);

    // 确保抢答结果包含用户信息
    const enhancedAnswerData = {
      ...answerData,
      userName: user.userName,
      userRole: user.role,
      timestamp: new Date().toISOString()
    };

    // 广播抢答更新给所有用户（主要是教师端）
    io.emit('quick-response-submitted', enhancedAnswerData);

    // 确认抢答提交成功
    socket.emit('quick-response-submit-confirmed', {
      success: true,
      responseId: answerData.responseId,
      message: '抢答提交成功'
    });
  });

  socket.on('end-quick-response', (responseId) => {
    const user = connectedUsers.get(socket.id);
    if (!user) return;

    console.log('抢答结束:', responseId, '由教师:', user.userName);

    // 广播抢答结束消息
    io.emit('quick-response-ended', {
      responseId,
      endedBy: user.userName,
      endedAt: new Date().toISOString()
    });

    // 确认抢答结束成功
    socket.emit('quick-response-end-confirmed', {
      success: true,
      responseId,
      message: '抢答已结束'
    });
  });

  // 考试相关事件
  // 教师发起考试
  socket.on('exam-start', (examData) => {
    try {
      const { id: examId, title, createdBy } = examData;
      
      if (!examId || !createdBy) {
        console.error('发起考试失败：缺少必要信息', examData);
        return;
      }
      
      console.log(`收到exam-start事件，考试ID: ${examId}, 标题: ${title || '未命名考试'}`);
      
      // 存储考试数据
      activeExams.set(examId, {
        ...examData,
        startTime: new Date().toISOString(),
        status: 'active'
      });
      
      // 找到发起考试的教师所在的课堂
      const teacherClassroom = Array.from(classrooms.values()).find(c => c.teacherId === createdBy);
      
      if (teacherClassroom) {
        // 向课堂中的所有学生广播考试开始信息
        console.log(`向课堂 ${teacherClassroom.id} 中的所有学生广播考试开始信息`);
        socket.to(teacherClassroom.id).emit('exam-start', examData);
      } else {
        console.log('未找到教师所在的课堂，考试将不会广播给学生');
      }
      
      console.log(`考试 "${title}" (ID: ${examId}) 已开始`);
    } catch (error) {
      console.error('处理考试开始事件时出错:', error);
    }
  });
  
  // 断开连接处理
  socket.on('disconnect', () => {
    const user = connectedUsers.get(socket.id);
    if (user) {
      console.log(`用户断开连接: ${user.userName} (${user.role})`);
      
      // 如果是教师断开连接
      if (user.role === '教师' && teacherSocket?.id === socket.id) {
        teacherSocket = null;
        console.log('教师已断开');
      }
      
      // 如果是被授予控制权的用户断开
      if (controlGrantedTo === socket.id) {
        controlGrantedTo = null;
        io.emit('control-revoked');
      }
      
      connectedUsers.delete(socket.id);
      
      // 广播更新在线用户列表
      io.emit('users-updated', Array.from(connectedUsers.values()));
    }
  });
});

// 服务器设置和启动
const PORT = process.env.PORT || 3002;
server.listen(PORT, () => {
  console.log(`WebSocket服务器运行在 http://localhost:${PORT}`);
}); 