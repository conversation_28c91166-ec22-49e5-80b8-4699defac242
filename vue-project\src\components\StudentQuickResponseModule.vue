<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { ElMessage, ElNotification } from 'element-plus';
import { Lightning, Timer, Clock } from '@element-plus/icons-vue';
import { getSocket } from '../services/socketService';

const props = defineProps({
  studentId: {
    type: String,
    required: true
  },
  studentName: {
    type: String,
    required: true
  }
});

// 抢答相关状态
const activeQuickResponses = ref([]);
const currentQuickResponse = ref(null);
const quickResponseVisible = ref(false);
const quickResponseAnswer = ref('');
const isSubmittingResponse = ref(false);
const countdown = ref(0);
let countdownTimer = null;

// 计算属性
const hasActiveResponse = computed(() => {
  return activeQuickResponses.value.some(r => r.status === 'active');
});

const latestActiveResponse = computed(() => {
  return activeQuickResponses.value.find(r => r.status === 'active');
});

// 处理抢答开始通知
const handleQuickResponseStart = (responseData) => {
  console.log('StudentQuickResponseModule: 收到抢答开始通知:', responseData);
  console.log('StudentQuickResponseModule: 当前活动抢答列表长度:', activeQuickResponses.value.length);

  // 验证数据完整性
  if (!responseData || !responseData.id || !responseData.title) {
    console.error('StudentQuickResponseModule: 抢答数据不完整:', responseData);
    return;
  }

  // 添加到活动抢答列表
  const existingIndex = activeQuickResponses.value.findIndex(r => r.id === responseData.id);
  if (existingIndex === -1) {
    activeQuickResponses.value.unshift(responseData);
    console.log('StudentQuickResponseModule: 新增抢答到列表，当前列表长度:', activeQuickResponses.value.length);
  } else {
    activeQuickResponses.value[existingIndex] = responseData;
    console.log('StudentQuickResponseModule: 更新现有抢答');
  }

  // 显示通知
  ElNotification({
    title: '新的抢答开始！',
    message: `${responseData.title} - 快来抢答吧！`,
    type: 'warning',
    duration: 5000,
    position: 'top-right',
    onClick: () => {
      joinQuickResponse(responseData);
    }
  });

  // 开始倒计时
  if (responseData.timeLimit) {
    startCountdown(responseData.timeLimit);
    console.log('StudentQuickResponseModule: 开始倒计时:', responseData.timeLimit, '秒');
  }

  // 强制触发响应式更新
  console.log('StudentQuickResponseModule: 抢答处理完成，当前活动抢答:', activeQuickResponses.value);
};

// 加入抢答
const joinQuickResponse = (response) => {
  currentQuickResponse.value = response;
  quickResponseVisible.value = true;
  quickResponseAnswer.value = '';
};

// 提交抢答
const submitQuickResponseAnswer = async () => {
  if (!currentQuickResponse.value) return;

  isSubmittingResponse.value = true;

  try {
    const answerData = {
      responseId: currentQuickResponse.value.id,
      userId: props.studentId,
      userName: props.studentName,
      answer: quickResponseAnswer.value || '已抢答',
      submittedAt: new Date().toISOString()
    };

    // 通过Socket提交抢答
    const socket = getSocket();
    if (socket) {
      socket.emit('submit-quick-response', answerData);
    }

    ElMessage.success('抢答提交成功！');
    quickResponseVisible.value = false;
    currentQuickResponse.value = null;
    quickResponseAnswer.value = '';

  } catch (error) {
    console.error('提交抢答失败:', error);
    ElMessage.error('抢答提交失败');
  } finally {
    isSubmittingResponse.value = false;
  }
};

// 开始倒计时
const startCountdown = (seconds) => {
  stopCountdown();
  countdown.value = seconds;
  
  countdownTimer = setInterval(() => {
    countdown.value--;
    
    if (countdown.value <= 0) {
      stopCountdown();
    }
  }, 1000);
};

// 停止倒计时
const stopCountdown = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
    countdownTimer = null;
  }
};

// 格式化倒计时
const formatCountdown = (seconds) => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// 处理抢答结束
const handleQuickResponseEnd = (data) => {
  console.log('抢答结束:', data);
  
  // 更新抢答状态
  const responseIndex = activeQuickResponses.value.findIndex(r => r.id === data.responseId);
  if (responseIndex !== -1) {
    activeQuickResponses.value[responseIndex].status = 'ended';
  }
  
  // 停止倒计时
  stopCountdown();
  
  // 如果当前正在参与这个抢答，关闭对话框
  if (currentQuickResponse.value && currentQuickResponse.value.id === data.responseId) {
    quickResponseVisible.value = false;
    currentQuickResponse.value = null;
  }
};

// 处理抢答提交确认
const handleQuickResponseSubmitConfirmed = (data) => {
  console.log('抢答提交确认:', data);
  if (data.success) {
    ElMessage.success('抢答提交成功！');
    quickResponseVisible.value = false;
    currentQuickResponse.value = null;
    quickResponseAnswer.value = '';
  }
};

// 调试方法：检查Socket连接状态
const checkSocketStatus = () => {
  const socket = getSocket();
  const status = {
    Socket存在: !!socket,
    Socket连接状态: socket ? socket.connected : false,
    Socket事件监听器: socket ? Object.keys(socket._callbacks || {}) : [],
    当前抢答列表长度: activeQuickResponses.value.length,
    学生ID: props.studentId,
    学生姓名: props.studentName
  };

  console.table(status);

  ElNotification({
    title: 'WebSocket连接状态',
    message: `
      Socket连接: ${status.Socket连接状态 ? '已连接' : '未连接'}
      抢答列表: ${status.当前抢答列表长度} 个
      学生: ${status.学生姓名} (${status.学生ID})
    `,
    type: status.Socket连接状态 ? 'success' : 'error',
    duration: 8000,
    position: 'top-right'
  });
};

// 调试方法：测试事件接收
const testSocketEvent = () => {
  const socket = getSocket();
  if (!socket) {
    ElMessage.error('Socket未连接');
    return;
  }

  // 手动触发一个测试抢答事件
  const testData = {
    id: 'test_' + Date.now(),
    title: '测试抢答',
    description: '这是一个测试抢答，用于验证事件接收',
    status: 'active',
    timeLimit: 30,
    teacherName: '测试教师'
  };

  console.log('手动触发测试抢答事件:', testData);
  handleQuickResponseStart(testData);

  ElMessage.success('已手动添加测试抢答');
};

// 调试方法：强制刷新
const forceRefresh = () => {
  // 重新设置事件监听器
  removeEventListeners();
  setTimeout(() => {
    setupEventListeners();
    ElMessage.success('已重新设置事件监听器');
  }, 500);
};

// 检查是否有活动的抢答
const checkForActiveQuickResponse = () => {
  console.log('StudentQuickResponseModule: 检查活动抢答');
  const socket = getSocket();
  if (!socket) {
    console.log('StudentQuickResponseModule: Socket未连接，无法检查活动抢答');
    return;
  }

  // 向服务器请求当前活动的抢答
  socket.emit('get-active-quick-response', {
    studentId: props.studentId,
    studentName: props.studentName
  });

  // 监听服务器响应
  socket.once('active-quick-response-response', (data) => {
    console.log('StudentQuickResponseModule: 收到活动抢答响应:', data);
    if (data && data.quickResponse) {
      console.log('StudentQuickResponseModule: 发现活动抢答，手动添加到列表');
      handleQuickResponseStart(data.quickResponse);
    } else {
      console.log('StudentQuickResponseModule: 当前没有活动的抢答');
    }
  });
};

// 设置事件监听器
const setupEventListeners = () => {
  const socket = getSocket();
  if (socket) {
    console.log('StudentQuickResponseModule: 设置事件监听器');
    socket.on('quick-response-started', handleQuickResponseStart);
    socket.on('quick-response-ended', handleQuickResponseEnd);
    socket.on('quick-response-submit-confirmed', handleQuickResponseSubmitConfirmed);
    return true;
  }
  return false;
};

// 移除事件监听器
const removeEventListeners = () => {
  const socket = getSocket();
  if (socket) {
    console.log('StudentQuickResponseModule: 移除事件监听器');
    socket.off('quick-response-started', handleQuickResponseStart);
    socket.off('quick-response-ended', handleQuickResponseEnd);
    socket.off('quick-response-submit-confirmed', handleQuickResponseSubmitConfirmed);
  }
};

// 组件挂载
onMounted(() => {
  console.log('StudentQuickResponseModule: 组件挂载');
  console.log('StudentQuickResponseModule: 学生信息 -', props.studentName, '(', props.studentId, ')');

  // 立即检查Socket状态
  const socket = getSocket();
  console.log('StudentQuickResponseModule: Socket状态 -', {
    存在: !!socket,
    连接状态: socket ? socket.connected : false,
    事件监听器数量: socket ? Object.keys(socket._callbacks || {}).length : 0
  });

  // 尝试立即设置事件监听器
  if (!setupEventListeners()) {
    console.log('StudentQuickResponseModule: Socket未就绪，启动重试机制');

    // 使用更积极的重试策略
    let retryCount = 0;
    const maxRetries = 20; // 增加重试次数
    const retryInterval = setInterval(() => {
      retryCount++;
      console.log(`StudentQuickResponseModule: 重试设置事件监听器 (${retryCount}/${maxRetries})`);

      if (setupEventListeners()) {
        clearInterval(retryInterval);
        console.log('StudentQuickResponseModule: 重试设置事件监听器成功');

        // 设置成功后，立即检查是否有活动的抢答
        checkForActiveQuickResponse();
      } else if (retryCount >= maxRetries) {
        clearInterval(retryInterval);
        console.error('StudentQuickResponseModule: 无法设置事件监听器，Socket连接可能有问题');
        ElMessage.error('抢答功能初始化失败，请刷新页面重试');
      }
    }, 500); // 减少重试间隔
  } else {
    // 如果立即设置成功，也检查活动抢答
    checkForActiveQuickResponse();
  }
});

// 组件卸载
onBeforeUnmount(() => {
  console.log('StudentQuickResponseModule: 组件卸载');
  stopCountdown();
  removeEventListeners();
});
</script>

<template>
  <div class="student-quick-response-module">
    <!-- 抢答模块标题 -->
    <div class="module-header">
      <h3>
        <el-icon><Lightning /></el-icon>
        课堂抢答
      </h3>
      <div class="header-info">
        <el-badge v-if="hasActiveResponse" :value="activeQuickResponses.filter(r => r.status === 'active').length" type="danger">
          <el-tag type="danger" size="small">进行中</el-tag>
        </el-badge>
        <!-- 调试信息 -->
        <el-tag v-if="activeQuickResponses.length > 0" type="info" size="small" style="margin-left: 8px;">
          总数: {{ activeQuickResponses.length }}
        </el-tag>
      </div>
    </div>

    <!-- 活动抢答列表 -->
    <div v-if="activeQuickResponses.length > 0" class="quick-response-list">
      <el-card 
        v-for="response in activeQuickResponses" 
        :key="response.id" 
        class="quick-response-card"
        :class="{ 'active-response': response.status === 'active' }"
      >
        <template #header>
          <div class="response-header">
            <div class="response-title">
              <h4>{{ response.title }}</h4>
              <div class="response-meta">
                <el-tag 
                  :type="response.status === 'active' ? 'danger' : 'info'" 
                  size="small"
                >
                  {{ response.status === 'active' ? '进行中' : '已结束' }}
                </el-tag>
                <span v-if="response.teacherName" class="teacher-name">
                  {{ response.teacherName }}
                </span>
              </div>
            </div>
            
            <!-- 倒计时显示 -->
            <div v-if="response.status === 'active' && countdown > 0" class="countdown-display">
              <el-icon><Timer /></el-icon>
              <span class="countdown-text">{{ formatCountdown(countdown) }}</span>
            </div>
          </div>
        </template>

        <!-- 抢答描述 -->
        <div v-if="response.description" class="response-description">
          {{ response.description }}
        </div>

        <!-- 抢答操作 -->
        <div class="response-actions">
          <el-button 
            v-if="response.status === 'active'" 
            @click="joinQuickResponse(response)" 
            type="danger" 
            size="large"
            class="response-button"
          >
            <el-icon><Lightning /></el-icon>
            立即抢答
          </el-button>
          <el-button v-else size="small" type="info" disabled>
            抢答已结束
          </el-button>
        </div>

        <!-- 时间限制提示 -->
        <div v-if="response.timeLimit && response.status === 'active'" class="time-limit-tip">
          <el-icon><Clock /></el-icon>
          时间限制：{{ response.timeLimit }}秒
        </div>
      </el-card>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <el-empty description="暂无抢答活动">
        <template #image>
          <el-icon size="64" color="#c0c4cc"><Lightning /></el-icon>
        </template>
        <template #default>
          <div class="debug-info">
            <el-button @click="checkSocketStatus" type="primary" size="small">
              检查连接状态
            </el-button>
            <el-button @click="testSocketEvent" type="warning" size="small">
              测试事件接收
            </el-button>
            <el-button @click="forceRefresh" type="success" size="small">
              强制刷新
            </el-button>
          </div>
        </template>
      </el-empty>
    </div>

    <!-- 抢答对话框 -->
    <el-dialog
      v-model="quickResponseVisible"
      title="课堂抢答"
      width="500px"
      :before-close="() => { quickResponseVisible = false; currentQuickResponse = null }"
    >
      <div v-if="currentQuickResponse" class="quick-response-content">
        <div class="response-info">
          <h3>{{ currentQuickResponse.title }}</h3>
          <p v-if="currentQuickResponse.description">{{ currentQuickResponse.description }}</p>
          <p class="time-limit" v-if="currentQuickResponse.timeLimit">
            时间限制：{{ currentQuickResponse.timeLimit }}秒
          </p>
        </div>

        <el-form>
          <el-form-item label="您的答案">
            <el-input
              v-model="quickResponseAnswer"
              type="textarea"
              :rows="3"
              placeholder="请输入您的答案（可选）"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="quickResponseVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="submitQuickResponseAnswer"
            :loading="isSubmittingResponse"
          >
            提交抢答
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.student-quick-response-module {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.module-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #f56c6c;
  font-size: 18px;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quick-response-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quick-response-card {
  border-left: 4px solid #dcdfe6;
  transition: all 0.3s ease;
}

.quick-response-card.active-response {
  border-left-color: #f56c6c;
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.15);
}

.response-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.response-title h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.response-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.teacher-name {
  font-size: 12px;
  color: #909399;
}

.countdown-display {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #f56c6c;
  font-weight: bold;
  font-size: 16px;
}

.response-description {
  margin: 12px 0;
  color: #606266;
  line-height: 1.5;
}

.response-actions {
  margin-top: 16px;
  text-align: center;
}

.response-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: bold;
}

.time-limit-tip {
  margin-top: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  color: #e6a23c;
  font-size: 12px;
  background: #fdf6ec;
  padding: 8px 12px;
  border-radius: 4px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.debug-info {
  margin-top: 16px;
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.quick-response-content .response-info h3 {
  color: #f56c6c;
  margin-bottom: 16px;
}

.time-limit {
  color: #e6a23c;
  font-weight: 500;
}
</style>
